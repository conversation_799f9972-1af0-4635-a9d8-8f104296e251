#!/usr/bin/env python3
"""
Test script to verify component registration in the deployed environment.
This script can be run in the deployed container to diagnose component registration issues.
"""

import sys
import os
import importlib
import traceback

# Add the app directory to the Python path
sys.path.insert(0, '/app')

def test_component_registration():
    """Test component registration step by step."""
    print("=== Component Registration Test ===")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    
    # Test 1: Check if app directory exists
    print("\n1. Checking app directory structure...")
    app_dir = "/app/app"
    if os.path.exists(app_dir):
        print(f"✓ App directory exists: {app_dir}")
        components_dir = os.path.join(app_dir, "components")
        if os.path.exists(components_dir):
            print(f"✓ Components directory exists: {components_dir}")
            files = os.listdir(components_dir)
            print(f"✓ Found {len(files)} files in components directory:")
            for file in sorted(files):
                print(f"  - {file}")
        else:
            print(f"✗ Components directory missing: {components_dir}")
            return False
    else:
        print(f"✗ App directory missing: {app_dir}")
        return False
    
    # Test 2: Check if select_data_component.py exists
    print("\n2. Checking select_data_component.py...")
    select_data_file = os.path.join(components_dir, "select_data_component.py")
    if os.path.exists(select_data_file):
        print(f"✓ select_data_component.py exists: {select_data_file}")
        # Check file size and permissions
        stat = os.stat(select_data_file)
        print(f"  File size: {stat.st_size} bytes")
        print(f"  File permissions: {oct(stat.st_mode)}")
    else:
        print(f"✗ select_data_component.py missing: {select_data_file}")
        return False
    
    # Test 3: Try to import the component system
    print("\n3. Testing component system import...")
    try:
        from app.core_.component_system import COMPONENT_REGISTRY, register_component
        print("✓ Successfully imported component system")
        print(f"  Initial registry: {list(COMPONENT_REGISTRY.keys())}")
    except Exception as e:
        print(f"✗ Failed to import component system: {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Try to import select_data_component directly
    print("\n4. Testing direct import of select_data_component...")
    try:
        import app.components.select_data_component
        print("✓ Successfully imported select_data_component")
        print(f"  Registry after import: {list(COMPONENT_REGISTRY.keys())}")
        
        # Check if SelectDataComponent is registered
        if "SelectDataComponent" in COMPONENT_REGISTRY:
            print("✓ SelectDataComponent is registered")
            component_class = COMPONENT_REGISTRY["SelectDataComponent"]
            print(f"  Component class: {component_class}")
            print(f"  Component module: {component_class.__module__}")
        else:
            print("✗ SelectDataComponent is NOT registered")
            return False
            
    except Exception as e:
        print(f"✗ Failed to import select_data_component: {e}")
        traceback.print_exc()
        return False
    
    # Test 5: Try component discovery mechanism
    print("\n5. Testing component discovery mechanism...")
    try:
        from app.core_.component_system import discover_component_modules
        imported_modules = discover_component_modules()
        print(f"✓ Component discovery completed")
        print(f"  Imported {len(imported_modules)} modules: {imported_modules}")
        print(f"  Final registry: {list(COMPONENT_REGISTRY.keys())}")
        
        if "SelectDataComponent" in COMPONENT_REGISTRY:
            print("✓ SelectDataComponent found in final registry")
        else:
            print("✗ SelectDataComponent missing from final registry")
            return False
            
    except Exception as e:
        print(f"✗ Component discovery failed: {e}")
        traceback.print_exc()
        return False
    
    print("\n=== All tests passed! ===")
    return True

if __name__ == "__main__":
    success = test_component_registration()
    sys.exit(0 if success else 1)
